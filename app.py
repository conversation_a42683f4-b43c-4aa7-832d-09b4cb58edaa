from flask import Flask, render_template, request, redirect, url_for, session
import json
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

@app.route('/')
def index():
    return render_template('form.html')

@app.route('/submit', methods=['POST'])
def submit():
    # 获取表单数据
    data = {
        'date': request.form.get('date'),
        'headline_news': request.form.get('headline_news'),
        'data_focus': request.form.get('data_focus'),
        'event_overview': request.form.get('event_overview'),
        'news_list': [],
        'daily_events': [],
        'report_charts': [],
        'main_index': []
    }
    
    # 处理新闻列表
    news_titles = request.form.getlist('news_title')
    news_contents = request.form.getlist('news_content')
    for i in range(len(news_titles)):
        if i < len(news_contents):
            data['news_list'].append({
                'title': news_titles[i],
                'content': news_contents[i]
            })
    
    # 处理每日事件
    event_industries = request.form.getlist('event_industry')
    event_contents = request.form.getlist('event_content')
    for i in range(len(event_industries)):
        if i < len(event_contents):
            data['daily_events'].append({
                'industry': event_industries[i],
                'content': event_contents[i]
            })
    
    # 处理报告图表
    chart_titles = request.form.getlist('chart_title')
    chart_urls = request.form.getlist('chart_url')
    for i in range(len(chart_titles)):
        if i < len(chart_urls):
            data['report_charts'].append({
                'title': chart_titles[i],
                'url': chart_urls[i]
            })
    
    # 处理主体索引
    index_names = request.form.getlist('index_name')
    index_identities = request.form.getlist('index_identity')
    index_ids = request.form.getlist('index_id')
    for i in range(len(index_names)):
        if i < len(index_identities) and i < len(index_ids):
            data['main_index'].append({
                'name': index_names[i],
                'identity': index_identities[i],
                'id': index_ids[i]
            })
    
    # 将数据存储到session中
    session['report_data'] = data
    
    return redirect(url_for('preview'))

@app.route('/preview')
def preview():
    data = session.get('report_data', {})
    if not data:
        return redirect(url_for('index'))
    
    return render_template('preview.html', data=data)

if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=5000)
