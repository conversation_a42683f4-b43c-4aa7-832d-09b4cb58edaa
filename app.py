from flask import Flask, render_template, request, redirect, url_for, session, send_file, jsonify
import json
import base64
import os
import uuid
import smtplib
from datetime import datetime
from markupsafe import Markup
from urllib.parse import quote
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import io

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# 创建上传目录
UPLOAD_FOLDER = 'static/uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# 邮件配置
try:
    from email_config import EMAIL_CONFIG
except ImportError:
    # 如果没有配置文件，使用默认配置
    EMAIL_CONFIG = {
        'smtp_server': 'smtp.qq.com',
        'smtp_port': 587,
        'sender_email': '<EMAIL>',
        'sender_password': 'your_app_password',
        'recipient_email': '<EMAIL>'
    }

# 添加自定义过滤器来处理换行符
@app.template_filter('nl2br')
def nl2br_filter(text):
    """将换行符转换为HTML的<br>标签"""
    if text is None:
        return ''
    # 将\r\n, \r, \n都转换为<br>
    text = str(text).replace('\r\n', '<br>').replace('\r', '<br>').replace('\n', '<br>')
    return Markup(text)

# 添加自定义过滤器来转换图片路径为base64
@app.template_filter('to_base64')
def to_base64_filter(image_path):
    """将图片路径转换为base64数据"""
    return get_image_base64(image_path)

# 添加自定义过滤器来生成主体URL
@app.template_filter('generate_entity_url')
def generate_entity_url_filter(entity):
    """根据主体身份和ID生成URL"""
    return generate_entity_url(entity['identity'], entity['id'])

def generate_entity_url(identity, entity_id):
    """根据身份和ID生成完整的URL"""
    if not identity or not entity_id:
        return "#"

    # 定义身份对应的URL路径
    url_mapping = {
        '企业': 'https://www.cvsource.com.cn/cvs/company/',
        '品牌': 'https://www.cvsource.com.cn/cvs/brand/',
        'GP': 'https://www.cvsource.com.cn/cvs/investor/',
        'LP': 'https://www.cvsource.com.cn/cvs/lp/',
        '基金': 'https://www.cvsource.com.cn/cvs/fund/'
    }

    base_url = url_mapping.get(identity)
    if not base_url:
        return "#"

    # 创建JSON对象并转换为base64
    company_data = json.dumps({"companyId": entity_id})
    company_base64 = base64.b64encode(company_data.encode('utf-8')).decode('utf-8')

    return f"{base_url}{company_base64}"

def send_email_with_html(html_content, filename, recipient_email=None):
    """发送HTML正文邮件"""
    try:
        # 使用配置中的收件人邮箱，或者传入的邮箱
        to_email = recipient_email or EMAIL_CONFIG['recipient_email']

        # 创建邮件对象
        msg = MIMEMultipart('alternative')
        msg['From'] = EMAIL_CONFIG['sender_email']
        msg['To'] = to_email
        msg['Subject'] = f"股权投资市场每日资讯 - {datetime.now().strftime('%Y-%m-%d')}"

        # 纯文本版本（作为备用）
        text_body = f"""
股权投资市场每日资讯

报告日期：{datetime.now().strftime('%Y年%m月%d日')}

此邮件包含HTML格式的详细报告内容。
如果您无法正常查看，请使用支持HTML的邮件客户端。

此邮件由系统自动发送，请勿回复。
        """

        # 添加纯文本版本
        part1 = MIMEText(text_body, 'plain', 'utf-8')
        msg.attach(part1)

        # 添加HTML版本作为邮件正文
        part2 = MIMEText(html_content, 'html', 'utf-8')
        msg.attach(part2)

        # 发送邮件
        server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'])
        server.starttls()
        server.login(EMAIL_CONFIG['sender_email'], EMAIL_CONFIG['sender_password'])
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG['sender_email'], to_email, text)
        server.quit()

        return True, "邮件发送成功"

    except Exception as e:
        return False, f"邮件发送失败: {str(e)}"

@app.route('/')
def index():
    # 获取session中保存的数据，如果有的话
    data = session.get('report_data', {})
    return render_template('form.html', data=data)

def save_uploaded_image(file):
    """保存上传的图片文件并返回文件路径"""
    if file and file.filename:
        # 生成唯一文件名
        file_ext = file.filename.lower().split('.')[-1]
        if file_ext not in ['jpg', 'jpeg', 'png', 'gif', 'webp']:
            file_ext = 'jpg'

        filename = f"{uuid.uuid4().hex}.{file_ext}"
        filepath = os.path.join(UPLOAD_FOLDER, filename)

        # 保存文件
        file.save(filepath)

        # 返回相对路径用于URL
        return f"uploads/{filename}"
    return None

def get_image_base64(image_path):
    """根据图片路径返回base64数据"""
    if image_path and os.path.exists(f"static/{image_path}"):
        with open(f"static/{image_path}", 'rb') as f:
            file_content = f.read()
            base64_string = base64.b64encode(file_content).decode('utf-8')
            # 从文件扩展名确定MIME类型
            file_ext = image_path.lower().split('.')[-1]
            mime_type = f"image/{file_ext}"
            return f"data:{mime_type};base64,{base64_string}"
    return None

@app.route('/submit', methods=['POST'])
def submit():
    # 获取表单数据
    data = {
        'date': request.form.get('date'),
        'headline_news': request.form.get('headline_news'),
        'data_focus': request.form.get('data_focus'),
        'event_overview': request.form.get('event_overview'),
        'news_list': [],
        'daily_events': [],
        'chart_title': request.form.get('chart_title'),
        'chart_url': request.form.get('chart_url'),
        'chart_image': None,
        'main_index': []
    }
    
    # 处理新闻列表
    news_titles = request.form.getlist('news_title')
    news_contents = request.form.getlist('news_content')
    news_images = request.files.getlist('news_image')

    # 获取已存在的数据以保留原有图片
    existing_data = session.get('report_data', {})
    existing_news = existing_data.get('news_list', [])

    for i in range(len(news_titles)):
        # 保留原有图片路径（如果存在）
        existing_image = existing_news[i]['image'] if i < len(existing_news) else None

        news_item = {
            'title': news_titles[i],
            'content': news_contents[i] if i < len(news_contents) else '',
            'image': existing_image  # 先使用原有图片
        }

        # 处理新闻图片（如果有新上传的图片）
        if i < len(news_images) and news_images[i].filename:
            news_item['image'] = save_uploaded_image(news_images[i])

        data['news_list'].append(news_item)
    
    # 处理每日事件
    event_industries = request.form.getlist('event_industry')
    event_contents = request.form.getlist('event_content')
    for i in range(len(event_industries)):
        if i < len(event_contents):
            data['daily_events'].append({
                'industry': event_industries[i],
                'content': event_contents[i]
            })
    
    # 处理图表图片
    chart_image_file = request.files.get('chart_image')
    if chart_image_file and chart_image_file.filename:
        data['chart_image'] = save_uploaded_image(chart_image_file)
    else:
        # 保留原有图表图片
        data['chart_image'] = existing_data.get('chart_image')
    
    # 处理主体索引
    index_names = request.form.getlist('index_name')
    index_identities = request.form.getlist('index_identity')
    index_ids = request.form.getlist('index_id')
    for i in range(len(index_names)):
        if i < len(index_identities) and i < len(index_ids):
            data['main_index'].append({
                'name': index_names[i],
                'identity': index_identities[i],
                'id': index_ids[i]
            })
    
    # 将数据存储到session中
    session['report_data'] = data
    
    return redirect(url_for('preview'))

@app.route('/preview')
def preview():
    data = session.get('report_data', {})
    if not data:
        return redirect(url_for('index'))

    return render_template('preview.html', data=data)

@app.route('/clear-session', methods=['POST'])
def clear_session():
    session.pop('report_data', None)
    return {'status': 'success'}

@app.route('/send-email', methods=['POST'])
def send_email():
    """发送HTML邮件"""
    try:
        data = session.get('report_data', {})
        if not data:
            return jsonify({'success': False, 'message': '没有可发送的数据'})

        # 生成HTML内容（与下载功能相同的逻辑）
        css_content = ""
        try:
            with open('static/css/preview.css', 'r', encoding='utf-8') as f:
                css_content = f.read()
        except:
            pass

        # 读取并转换logo图片为base64
        logo_base64 = ""
        try:
            with open('static/logo/logo.png', 'rb') as f:
                logo_content = f.read()
                logo_base64 = base64.b64encode(logo_content).decode('utf-8')
                logo_base64 = f"data:image/png;base64,{logo_base64}"
        except:
            pass

        # 读取并转换图标为base64
        icons = {}
        icon_files = ['u14.png', 'u27.png', 'u28.png', 'u29.png']
        for icon_file in icon_files:
            try:
                with open(f'static/icon/{icon_file}', 'rb') as f:
                    icon_content = f.read()
                    icon_base64 = base64.b64encode(icon_content).decode('utf-8')
                    icons[icon_file] = f"data:image/png;base64,{icon_base64}"
            except:
                icons[icon_file] = ""

        # 渲染HTML内容
        html_content = render_template('download.html',
                                     data=data,
                                     css_content=css_content,
                                     logo_base64=logo_base64,
                                     icons=icons)

        # 生成文件名
        filename = f"股权投资市场每日资讯_{data.get('date', '未知日期')}.html"

        # 发送邮件
        success, message = send_email_with_html(html_content, filename)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': f'发送失败: {str(e)}'})

@app.route('/download-html')
def download_html():
    """生成包含完整样式和base64图片的HTML文件供下载"""
    data = session.get('report_data', {})
    if not data:
        return redirect(url_for('index'))

    # 读取CSS文件内容
    css_content = ""
    try:
        with open('static/css/preview.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
    except:
        pass

    # 读取并转换logo图片为base64
    logo_base64 = ""
    try:
        with open('static/logo/logo.png', 'rb') as f:
            logo_content = f.read()
            logo_base64 = base64.b64encode(logo_content).decode('utf-8')
            logo_base64 = f"data:image/png;base64,{logo_base64}"
    except:
        pass

    # 读取并转换图标为base64
    icons = {}
    icon_files = ['u14.png', 'u27.png', 'u28.png', 'u29.png']
    for icon_file in icon_files:
        try:
            with open(f'static/icon/{icon_file}', 'rb') as f:
                icon_content = f.read()
                icon_base64 = base64.b64encode(icon_content).decode('utf-8')
                icons[icon_file] = f"data:image/png;base64,{icon_base64}"
        except:
            icons[icon_file] = ""

    # 渲染下载模板
    from flask import Response
    html_content = render_template('download.html',
                                 data=data,
                                 css_content=css_content,
                                 logo_base64=logo_base64,
                                 icons=icons)

    # 生成文件名
    filename = f"股权投资市场每日资讯_{data.get('date', '未知日期')}.html"
    # URL编码文件名以支持中文
    encoded_filename = quote(filename.encode('utf-8'))

    return Response(
        html_content,
        mimetype='text/html',
        headers={
            'Content-Disposition': f'attachment; filename*=UTF-8\'\'{encoded_filename}'
        }
    )

if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=5000)
