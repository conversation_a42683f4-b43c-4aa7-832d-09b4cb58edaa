<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日报信息填写</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <h1>日报信息填写</h1>
        <form action="/submit" method="POST" class="report-form">
            
            <!-- 日期 -->
            <div class="form-section">
                <h2>基本信息</h2>
                <div class="form-group">
                    <label for="date">日期:</label>
                    <input type="date" id="date" name="date" value="{{ data.date or '' }}" required>
                </div>
            </div>

            <!-- 今日导读 -->
            <div class="form-section">
                <h2>今日导读</h2>
                <div class="form-group">
                    <label for="headline_news">📰 头条要闻:</label>
                    <input type="text" id="headline_news" name="headline_news" value="{{ data.headline_news or '' }}" placeholder="例如：50万亿，VC打工了医生，律师的钱包">
                </div>
                <div class="form-group">
                    <label for="data_focus">📊 数图聚焦:</label>
                    <input type="text" id="data_focus" name="data_focus" value="{{ data.data_focus or '' }}" placeholder="例如：2015-2024年"TOP机构"投资交易数量占比及市场覆盖率">
                </div>
                <div class="form-group">
                    <label for="event_overview">🔵 事件概览:</label>
                    <input type="text" id="event_overview" name="event_overview" value="{{ data.event_overview or '' }}" placeholder="例如："医疗健康"以8起案例位列活跃行业首位；"云南鑫耀完成4亿元人民币B轮融资"成为交易规模最大案例">
                </div>
            </div>

            <!-- 新闻列表 -->
            <div class="form-section">
                <h2>新闻列表</h2>
                <div id="news-container">
                    {% if data.news_list and data.news_list|length > 0 %}
                        {% for news in data.news_list %}
                        <div class="news-item">
                            <div class="form-group">
                                <label>新闻标题:</label>
                                <input type="text" name="news_title" value="{{ news.title or '' }}" placeholder="请输入新闻标题">
                            </div>
                            <div class="form-group">
                                <label>新闻内容:</label>
                                <textarea name="news_content" placeholder="请输入新闻内容" rows="3">{{ news.content or '' }}</textarea>
                            </div>
                            {% if not loop.first %}
                            <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="news-item">
                        <div class="form-group">
                            <label>新闻标题:</label>
                            <input type="text" name="news_title" placeholder="请输入新闻标题">
                        </div>
                        <div class="form-group">
                            <label>新闻内容:</label>
                            <textarea name="news_content" placeholder="请输入新闻内容" rows="3"></textarea>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <button type="button" onclick="addNews()" class="add-btn">添加新闻</button>
            </div>

            <!-- 每日事件 -->
            <div class="form-section">
                <h2>每日事件</h2>
                <div id="event-container">
                    {% if data.daily_events and data.daily_events|length > 0 %}
                        {% for event in data.daily_events %}
                        <div class="event-item">
                            <div class="form-group">
                                <label>行业:</label>
                                <input type="text" name="event_industry" value="{{ event.industry or '' }}" placeholder="请输入行业">
                            </div>
                            <div class="form-group">
                                <label>事件内容:</label>
                                <textarea name="event_content" placeholder="请输入事件内容" rows="3">{{ event.content or '' }}</textarea>
                            </div>
                            {% if not loop.first %}
                            <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="event-item">
                        <div class="form-group">
                            <label>行业:</label>
                            <input type="text" name="event_industry" placeholder="请输入行业">
                        </div>
                        <div class="form-group">
                            <label>事件内容:</label>
                            <textarea name="event_content" placeholder="请输入事件内容" rows="3"></textarea>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <button type="button" onclick="addEvent()" class="add-btn">添加事件</button>
            </div>

            <!-- 报告图表 -->
            <div class="form-section">
                <h2>报告图表</h2>
                <div id="chart-container">
                    {% if data.report_charts and data.report_charts|length > 0 %}
                        {% for chart in data.report_charts %}
                        <div class="chart-item">
                            <div class="form-group">
                                <label>图表标题:</label>
                                <input type="text" name="chart_title" value="{{ chart.title or '' }}" placeholder="请输入图表标题">
                            </div>
                            <div class="form-group">
                                <label>图表地址:</label>
                                <input type="url" name="chart_url" value="{{ chart.url or '' }}" placeholder="请输入图表地址">
                            </div>
                            {% if not loop.first %}
                            <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="chart-item">
                        <div class="form-group">
                            <label>图表标题:</label>
                            <input type="text" name="chart_title" placeholder="请输入图表标题">
                        </div>
                        <div class="form-group">
                            <label>图表地址:</label>
                            <input type="url" name="chart_url" placeholder="请输入图表地址">
                        </div>
                    </div>
                    {% endif %}
                </div>
                <button type="button" onclick="addChart()" class="add-btn">添加图表</button>
            </div>

            <!-- 主体索引 -->
            <div class="form-section">
                <h2>主体索引</h2>
                <div id="index-container">
                    {% if data.main_index and data.main_index|length > 0 %}
                        {% for index in data.main_index %}
                        <div class="index-item">
                            <div class="form-group">
                                <label>名字:</label>
                                <input type="text" name="index_name" value="{{ index.name or '' }}" placeholder="请输入名字">
                            </div>
                            <div class="form-group">
                                <label>身份:</label>
                                <input type="text" name="index_identity" value="{{ index.identity or '' }}" placeholder="请输入身份">
                            </div>
                            <div class="form-group">
                                <label>ID:</label>
                                <input type="text" name="index_id" value="{{ index.id or '' }}" placeholder="请输入ID">
                            </div>
                            {% if not loop.first %}
                            <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="index-item">
                        <div class="form-group">
                            <label>名字:</label>
                            <input type="text" name="index_name" placeholder="请输入名字">
                        </div>
                        <div class="form-group">
                            <label>身份:</label>
                            <input type="text" name="index_identity" placeholder="请输入身份">
                        </div>
                        <div class="form-group">
                            <label>ID:</label>
                            <input type="text" name="index_id" placeholder="请输入ID">
                        </div>
                    </div>
                    {% endif %}
                </div>
                <button type="button" onclick="addIndex()" class="add-btn">添加索引</button>
            </div>

            <div class="form-actions">
                <button type="submit" class="submit-btn">生成预览</button>
                <button type="button" onclick="clearForm()" class="clear-btn">清空表单</button>
            </div>
        </form>
    </div>

    <script>


        function addNews() {
            const container = document.getElementById('news-container');
            const newItem = document.createElement('div');
            newItem.className = 'news-item';
            newItem.innerHTML = `
                <div class="form-group">
                    <label>新闻标题:</label>
                    <input type="text" name="news_title" placeholder="请输入新闻标题">
                </div>
                <div class="form-group">
                    <label>新闻内容:</label>
                    <textarea name="news_content" placeholder="请输入新闻内容" rows="3"></textarea>
                </div>
                <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
            `;
            container.appendChild(newItem);
        }

        function addEvent() {
            const container = document.getElementById('event-container');
            const newItem = document.createElement('div');
            newItem.className = 'event-item';
            newItem.innerHTML = `
                <div class="form-group">
                    <label>行业:</label>
                    <input type="text" name="event_industry" placeholder="请输入行业">
                </div>
                <div class="form-group">
                    <label>事件内容:</label>
                    <textarea name="event_content" placeholder="请输入事件内容" rows="3"></textarea>
                </div>
                <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
            `;
            container.appendChild(newItem);
        }

        function addChart() {
            const container = document.getElementById('chart-container');
            const newItem = document.createElement('div');
            newItem.className = 'chart-item';
            newItem.innerHTML = `
                <div class="form-group">
                    <label>图表标题:</label>
                    <input type="text" name="chart_title" placeholder="请输入图表标题">
                </div>
                <div class="form-group">
                    <label>图表地址:</label>
                    <input type="url" name="chart_url" placeholder="请输入图表地址">
                </div>
                <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
            `;
            container.appendChild(newItem);
        }

        function addIndex() {
            const container = document.getElementById('index-container');
            const newItem = document.createElement('div');
            newItem.className = 'index-item';
            newItem.innerHTML = `
                <div class="form-group">
                    <label>名字:</label>
                    <input type="text" name="index_name" placeholder="请输入名字">
                </div>
                <div class="form-group">
                    <label>身份:</label>
                    <input type="text" name="index_identity" placeholder="请输入身份">
                </div>
                <div class="form-group">
                    <label>ID:</label>
                    <input type="text" name="index_id" placeholder="请输入ID">
                </div>
                <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
            `;
            container.appendChild(newItem);
        }

        function removeItem(button) {
            button.parentElement.remove();
        }

        function clearForm() {
            if (confirm('确定要清空所有表单数据吗？此操作不可撤销。')) {
                // 清空所有输入框
                document.querySelectorAll('input, textarea').forEach(function(element) {
                    element.value = '';
                });

                // 重置动态添加的项目，只保留第一个
                resetContainer('news-container', 'news-item');
                resetContainer('event-container', 'event-item');
                resetContainer('chart-container', 'chart-item');
                resetContainer('index-container', 'index-item');

                // 发送请求清除session数据
                fetch('/clear-session', {method: 'POST'});
            }
        }

        function resetContainer(containerId, itemClass) {
            const container = document.getElementById(containerId);
            const items = container.querySelectorAll('.' + itemClass);

            // 保留第一个项目，删除其他项目
            for (let i = 1; i < items.length; i++) {
                items[i].remove();
            }

            // 清空第一个项目的内容
            if (items.length > 0) {
                const firstItem = items[0];
                firstItem.querySelectorAll('input, textarea').forEach(function(element) {
                    element.value = '';
                });
                // 移除删除按钮（如果有的话）
                const removeBtn = firstItem.querySelector('.remove-btn');
                if (removeBtn) {
                    removeBtn.remove();
                }
            }
        }
    </script>
</body>
</html>
