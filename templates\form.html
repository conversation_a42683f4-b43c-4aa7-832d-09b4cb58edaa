<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日报信息填写</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <h1>日报信息填写</h1>
        <form action="/submit" method="POST" enctype="multipart/form-data" class="report-form">
            
            <!-- 日期 -->
            <div class="form-section">
                <h2>基本信息</h2>
                <div class="form-group">
                    <label for="date">日期:</label>
                    <input type="date" id="date" name="date" value="{{ data.date or '' }}" required>
                </div>
            </div>

            <!-- 今日导读 -->
            <div class="form-section">
                <h2>今日导读</h2>
                <div class="form-group">
                    <label for="headline_news">📰 头条要闻:</label>
                    <input type="text" id="headline_news" name="headline_news" value="{{ data.headline_news or '' }}" placeholder="例如：50万亿，VC打工了医生，律师的钱包">
                </div>
                <div class="form-group">
                    <label for="data_focus">📊 数图聚焦:</label>
                    <input type="text" id="data_focus" name="data_focus" value="{{ data.data_focus or '' }}" placeholder="例如：2015-2024年"TOP机构"投资交易数量占比及市场覆盖率">
                </div>
                <div class="form-group">
                    <label for="event_overview">🔵 事件概览:</label>
                    <input type="text" id="event_overview" name="event_overview" value="{{ data.event_overview or '' }}" placeholder="例如："医疗健康"以8起案例位列活跃行业首位；"云南鑫耀完成4亿元人民币B轮融资"成为交易规模最大案例">
                </div>
            </div>

            <!-- 新闻列表 -->
            <div class="form-section">
                <h2>新闻列表</h2>
                <div id="news-container">
                    {% if data.news_list and data.news_list|length > 0 %}
                        {% for news in data.news_list %}
                        <div class="news-item">
                            <div class="form-group">
                                <label>新闻标题:</label>
                                <input type="text" name="news_title" value="{{ news.title or '' }}" placeholder="请输入新闻标题">
                            </div>
                            <div class="form-group">
                                <label>新闻内容:</label>
                                <textarea name="news_content" placeholder="请输入新闻内容" rows="8">{{ news.content or '' }}</textarea>
                            </div>
                            <div class="form-group">
                                <label>新闻图片:</label>
                                <input type="file" name="news_image" accept="image/*" onchange="previewImage(this, 'news-preview-{{ loop.index0 }}')">
                                <div id="news-preview-{{ loop.index0 }}" class="image-preview">
                                    {% if news.image %}
                                    <img src="{{ news.image|to_base64 }}" alt="当前图片" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
                                    {% endif %}
                                </div>
                            </div>
                            {% if not loop.first %}
                            <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="news-item">
                        <div class="form-group">
                            <label>新闻标题:</label>
                            <input type="text" name="news_title" placeholder="请输入新闻标题">
                        </div>
                        <div class="form-group">
                            <label>新闻内容:</label>
                            <textarea name="news_content" placeholder="请输入新闻内容" rows="8"></textarea>
                        </div>
                        <div class="form-group">
                            <label>新闻图片:</label>
                            <input type="file" name="news_image" accept="image/*" onchange="previewImage(this, 'news-preview-0')">
                            <div id="news-preview-0" class="image-preview">
                                {% if data.news_list and data.news_list|length > 0 and data.news_list[0].image %}
                                <img src="{{ data.news_list[0].image|to_base64 }}" alt="当前图片" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <button type="button" onclick="addNews()" class="add-btn">添加新闻</button>
            </div>

            <!-- 每日事件 -->
            <div class="form-section">
                <h2>每日事件</h2>
                <div id="event-container">
                    {% if data.daily_events and data.daily_events|length > 0 %}
                        {% for event in data.daily_events %}
                        <div class="event-item">
                            <div class="form-group">
                                <label>行业:</label>
                                <input type="text" name="event_industry" value="{{ event.industry or '' }}" placeholder="请输入行业">
                            </div>
                            <div class="form-group">
                                <label>事件内容:</label>
                                <textarea name="event_content" placeholder="请输入事件内容" rows="8">{{ event.content or '' }}</textarea>
                            </div>
                            {% if not loop.first %}
                            <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="event-item">
                        <div class="form-group">
                            <label>行业:</label>
                            <input type="text" name="event_industry" placeholder="请输入行业">
                        </div>
                        <div class="form-group">
                            <label>事件内容:</label>
                            <textarea name="event_content" placeholder="请输入事件内容" rows="8"></textarea>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <button type="button" onclick="addEvent()" class="add-btn">添加事件</button>
            </div>

            <!-- 数据图表 -->
            <div class="form-section">
                <h2>数据图表</h2>
                <div class="form-group">
                    <label>图表标题:</label>
                    <input type="text" name="chart_title" value="{{ data.chart_title or '' }}" placeholder="请输入图表标题">
                </div>
                <div class="form-group">
                    <label>图表地址:</label>
                    <input type="url" name="chart_url" value="{{ data.chart_url or '' }}" placeholder="请输入图表地址">
                </div>
                <div class="form-group">
                    <label>图表图片:</label>
                    <input type="file" name="chart_image" accept="image/*" onchange="previewImage(this, 'chart-preview')">
                    <div id="chart-preview" class="image-preview">
                        {% if data.chart_image %}
                        <img src="{{ data.chart_image|to_base64 }}" alt="当前图表图片" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 主体索引 -->
            <div class="form-section">
                <h2>主体索引</h2>
                <div class="table-container">
                    <table class="index-table">
                        <thead>
                            <tr>
                                <th>名字</th>
                                <th>身份</th>
                                <th>ID</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="index-table-body">
                            {% if data.main_index and data.main_index|length > 0 %}
                                {% for index in data.main_index %}
                                <tr class="index-row">
                                    <td>
                                        <input type="text" name="index_name" value="{{ index.name or '' }}" placeholder="请输入名字" class="table-input">
                                    </td>
                                    <td>
                                        <select name="index_identity" class="table-select">
                                            <option value="">请选择身份</option>
                                            <option value="企业" {% if index.identity == '企业' %}selected{% endif %}>企业</option>
                                            <option value="品牌" {% if index.identity == '品牌' %}selected{% endif %}>品牌</option>
                                            <option value="GP" {% if index.identity == 'GP' %}selected{% endif %}>GP</option>
                                            <option value="LP" {% if index.identity == 'LP' %}selected{% endif %}>LP</option>
                                            <option value="基金" {% if index.identity == '基金' %}selected{% endif %}>基金</option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text" name="index_id" value="{{ index.id or '' }}" placeholder="请输入ID" class="table-input">
                                    </td>
                                    <td>
                                        {% if not loop.first %}
                                        <button type="button" onclick="removeTableRow(this)" class="remove-btn-small">删除</button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                            <tr class="index-row">
                                <td>
                                    <input type="text" name="index_name" placeholder="请输入名字" class="table-input">
                                </td>
                                <td>
                                    <select name="index_identity" class="table-select">
                                        <option value="">请选择身份</option>
                                        <option value="企业">企业</option>
                                        <option value="品牌">品牌</option>
                                        <option value="GP">GP</option>
                                        <option value="LP">LP</option>
                                        <option value="基金">基金</option>
                                    </select>
                                </td>
                                <td>
                                    <input type="text" name="index_id" placeholder="请输入ID" class="table-input">
                                </td>
                                <td></td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                <button type="button" onclick="addIndexRow()" class="add-btn">添加主体</button>
            </div>

            <div class="form-actions">
                <button type="submit" class="submit-btn">生成预览</button>
                <button type="button" onclick="clearForm()" class="clear-btn">清空表单</button>
            </div>
        </form>
    </div>

    <script>


        function addNews() {
            const container = document.getElementById('news-container');
            const itemCount = container.children.length;
            const newItem = document.createElement('div');
            newItem.className = 'news-item';
            newItem.innerHTML = `
                <div class="form-group">
                    <label>新闻标题:</label>
                    <input type="text" name="news_title" placeholder="请输入新闻标题">
                </div>
                <div class="form-group">
                    <label>新闻内容:</label>
                    <textarea name="news_content" placeholder="请输入新闻内容" rows="8"></textarea>
                </div>
                <div class="form-group">
                    <label>新闻图片:</label>
                    <input type="file" name="news_image" accept="image/*" onchange="previewImage(this, 'news-preview-${itemCount}')">
                    <div id="news-preview-${itemCount}" class="image-preview"></div>
                </div>
                <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
            `;
            container.appendChild(newItem);
        }

        function addEvent() {
            const container = document.getElementById('event-container');
            const newItem = document.createElement('div');
            newItem.className = 'event-item';
            newItem.innerHTML = `
                <div class="form-group">
                    <label>行业:</label>
                    <input type="text" name="event_industry" placeholder="请输入行业">
                </div>
                <div class="form-group">
                    <label>事件内容:</label>
                    <textarea name="event_content" placeholder="请输入事件内容" rows="8"></textarea>
                </div>
                <button type="button" onclick="removeItem(this)" class="remove-btn">删除</button>
            `;
            container.appendChild(newItem);
        }

        // 图片预览功能
        function previewImage(input, previewId) {
            const preview = document.getElementById(previewId);
            const file = input.files[0];

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // 清除之前的内容，然后添加新图片
                    preview.innerHTML = `<img src="${e.target.result}" alt="预览图片" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">`;
                };
                reader.readAsDataURL(file);
            } else {
                // 如果没有选择文件，不要清除预览，保留可能存在的已保存图片
                // preview.innerHTML = '';
            }
        }

        function addIndexRow() {
            const tbody = document.getElementById('index-table-body');
            const newRow = document.createElement('tr');
            newRow.className = 'index-row';
            newRow.innerHTML = `
                <td>
                    <input type="text" name="index_name" placeholder="请输入名字" class="table-input">
                </td>
                <td>
                    <select name="index_identity" class="table-select">
                        <option value="">请选择身份</option>
                        <option value="企业">企业</option>
                        <option value="品牌">品牌</option>
                        <option value="GP">GP</option>
                        <option value="LP">LP</option>
                        <option value="基金">基金</option>
                    </select>
                </td>
                <td>
                    <input type="text" name="index_id" placeholder="请输入ID" class="table-input">
                </td>
                <td>
                    <button type="button" onclick="removeTableRow(this)" class="remove-btn-small">删除</button>
                </td>
            `;
            tbody.appendChild(newRow);
        }

        function removeTableRow(button) {
            const row = button.closest('tr');
            row.remove();
        }

        function removeItem(button) {
            button.parentElement.remove();
        }

        function clearForm() {
            if (confirm('确定要清空所有表单数据吗？此操作不可撤销。')) {
                // 清空所有输入框
                document.querySelectorAll('input, textarea').forEach(function(element) {
                    element.value = '';
                });

                // 重置动态添加的项目，只保留第一个
                resetContainer('news-container', 'news-item');
                resetContainer('event-container', 'event-item');

                // 重置主体索引表格，只保留第一行
                const indexTableBody = document.getElementById('index-table-body');
                const firstRow = indexTableBody.querySelector('.index-row');
                if (firstRow) {
                    // 清空第一行的内容
                    firstRow.querySelectorAll('input').forEach(input => input.value = '');
                    firstRow.querySelector('select').selectedIndex = 0;

                    // 删除其他行
                    const allRows = indexTableBody.querySelectorAll('.index-row');
                    for (let i = 1; i < allRows.length; i++) {
                        allRows[i].remove();
                    }
                }

                // 清除图片预览
                document.querySelectorAll('.image-preview').forEach(function(preview) {
                    preview.innerHTML = '';
                });

                // 发送请求清除session数据
                fetch('/clear-session', {method: 'POST'});
            }
        }

        function resetContainer(containerId, itemClass) {
            const container = document.getElementById(containerId);
            const items = container.querySelectorAll('.' + itemClass);

            // 保留第一个项目，删除其他项目
            for (let i = 1; i < items.length; i++) {
                items[i].remove();
            }

            // 清空第一个项目的内容
            if (items.length > 0) {
                const firstItem = items[0];
                firstItem.querySelectorAll('input, textarea').forEach(function(element) {
                    element.value = '';
                });
                // 移除删除按钮（如果有的话）
                const removeBtn = firstItem.querySelector('.remove-btn');
                if (removeBtn) {
                    removeBtn.remove();
                }
            }
        }
    </script>
</body>
</html>
