<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股权投资市场每日资讯 - {{ data.date or '未知日期' }}</title>
    <style>
        {{ css_content|safe }}
    </style>
</head>
<body>
    <div class="report-container">
        <!-- 头部 -->
        <div class="header">
            <div class="header-top">
                <div class="logo-section">
                    <div class="logo-container">
                        <img src="{{ logo_base64 }}" alt="嘉川数据" class="logo-image">
                    </div>
                    <div class="company-info">
                        <div class="company-name-cn">嘉川数据</div>
                        <div class="company-name-en">JIA CHUAN</div>
                    </div>
                </div>
            </div>
            <div class="header-bottom">
                <div class="report-title">股权投资市场每日资讯</div>
                <div class="report-date">{{ data.date }}</div>
            </div>
        </div>
 <!-- 今日导读 -->
        <div class="today-guide">
            <div class="guide-header">
                <h2 class="guide-title">今日导读</h2>
            </div>
            <div class="guide-content">
                <div class="guide-item">
                    <div class="guide-icon">
                        <img src="{{ icons['u27.png'] }}" alt="头条要闻" style="width: 20px;height: 20px;">
                    </div>
                    <div class="guide-text">
                        <span class="guide-label">头条要闻：</span>
                        <span class="guide-value">{{ data.headline_news }}</span>
                    </div>
                </div>
                <div class="guide-item">
                    <div class="guide-icon">
                        <img src="{{ icons['u28.png'] }}" alt="数图聚焦" style="width: 20px;height: 20px;">
                    </div>
                    <div class="guide-text">
                        <span class="guide-label">数图聚焦：</span>
                        <span class="guide-value">{{ data.data_focus }}</span>
                    </div>
                </div>
                <div class="guide-item">
                    <div class="guide-icon">
                        <img src="{{ icons['u29.png'] }}" alt="事件概览" style="width: 20px;height: 20px;">
                    </div>
                    <div class="guide-text">
                        <span class="guide-label"> 事件概览：</span>
                        <span class="guide-value">{{ data.event_overview }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- 左侧内容 -->
            <div class="left-column">
                <!-- 新闻内容 -->
                {% if data.news_list %}
                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">新闻内容</h3>
                    </div>
                    <div class="section-content">
                        {% for news in data.news_list %}
                        <div class="news-item">
                            <!-- 第一部分：标题 -->
                        
                            <h4 class="news-title">{{ news.title }}</h4>
                          

                            <!-- 第二部分：图片 -->
                            <div class="news-image-section">
                                {% if news.image %}
                                <div class="news-image">
                                    <img src="{{ news.image|to_base64 }}" alt="{{ news.title }}" class="news-img">
                                </div>
                                {% else %}
                                <div class="news-image-placeholder">
                                    <!-- 图片占位区域 -->
                                </div>
                                {% endif %}
                            </div>
                            
                            <!-- 第三部分：内容 -->
                            <div class="news-content-section">
                                <p class="news-text">{{ news.content|nl2br }}</p>
                                
                                <!-- 底部按钮和分享 -->
                                <div class="news-footer">
                                    <button class="read-full-btn">阅读全文</button>
                                    <div class="share-wechat">
                                        <span class="share-label">分享：</span>
                                        <img src="{{ icons['u14.png'] }}" alt="分享到微信" class="wechat-icon">
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- 每日事件 -->
                {% if data.daily_events %}
                <div class="content-section daily-events-section">
                    <div class="section-header">
                        <h3 class="section-title">新闻内容</h3>
                    </div>
                    <div class="section-content">
                        {% for event in data.daily_events %}
                        <div class="event-item">
                            <div class="event-industry">{{ event.industry }}</div>
                            <div class="event-content">{{ event.content|nl2br }}</div>
                            <div class="event-details-link">
                                <a href="#" class="view-details">查看交易详情&gt;</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- 数据图表 -->
                {% if data.chart_title or data.chart_image %}
                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">数据图表</h3>
                    </div>
                    <div class="section-content">
                        <div class="chart-item">
                            <div class="chart-image-container">
                                {% if data.chart_image %}
                                <div class="chart-image">
                                    <img src="{{ data.chart_image|to_base64 }}" alt="{{ data.chart_title or '数据图表' }}" class="chart-img">
                                </div>
                                {% else %}
                                <div class="chart-image-placeholder">
                                    <!-- 图表图片占位区域 -->
                                </div>
                                {% endif %}
                            </div>
                            {% if data.chart_title and data.chart_url %}
                            <div class="chart-source">
                               源自： <a href="{{ data.chart_url }}" target="_blank" class="chart-source-link">{{ data.chart_title }}</a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- 右侧边栏 -->
            <div class="right-sidebar">

                <!-- 主体索引 -->
                {% if data.main_index %}
                <div class="sidebar-section">
                    <h3 class="sidebar-title">主体索引</h3>
                    <div class="index-list">
                        {% for index in data.main_index %}
                        <div class="index-item">
                            <div class="index-name">
                                <a href="{{ index|generate_entity_url }}" target="_blank" class="entity-link">{{ index.name }}</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

          
            </div>
        </div>
    </div>
</body>
</html>
