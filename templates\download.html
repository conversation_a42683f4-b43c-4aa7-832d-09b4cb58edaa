<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股权投资市场每日资讯 - {{ data.date or '未知日期' }}</title>
    <!-- 移除 style 标签 -->
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0;">
    <div class="report-container" style="max-width: 1200px; margin: 0 auto; padding: 20px;">
        <!-- 头部 -->
        <div class="header" style="margin-bottom: 20px;">
            <div class="header-top" style="display: flex; align-items: center; justify-content: space-between;">
                <div class="logo-section" style="display: flex; align-items: center;">
                    <div class="logo-container" style="margin-right: 10px;">
                        <img src="{{ logo_base64 }}" alt="嘉川数据" class="logo-image" style="max-width: 100px; height: auto;">
                    </div>
                    <div class="company-info" style="overflow: hidden">
                        <div class="company-name-cn" style="font-size: 18px; font-weight: bold;float: left">嘉川数据</div>
                        <div class="company-name-en" style="font-size: 14px; color: #666;float: right;">JIA CHUAN</div>
                    </div>
                </div>
            </div>
            <div class="header-bottom" style="margin-top: 10px;">
                <div class="report-title" style="font-size: 24px; font-weight: bold;">股权投资市场每日资讯</div>
                <div class="report-date" style="font-size: 16px; color: #666;">{{ data.date }}</div>
            </div>
        </div>
        <!-- 今日导读 -->
        <div class="today-guide" style="margin-bottom: 20px;">
            <div class="guide-header" style="margin-bottom: 10px;">
                <h2 class="guide-title" style="font-size: 20px; font-weight: bold;">今日导读</h2>
            </div>
            <div class="guide-content">
                <div class="guide-item" style="display: flex; align-items: center; margin-bottom: 10px;">
                    <div class="guide-icon" style="margin-right: 10px;">
                        <img src="{{ icons['u27.png'] }}" alt="头条要闻" style="width: 20px;height: 20px;">
                    </div>
                    <div class="guide-text">
                        <span class="guide-label" style="font-weight: bold;">头条要闻：</span>
                        <span class="guide-value" style="margin-left: 5px;">{{ data.headline_news }}</span>
                    </div>
                </div>
                <div class="guide-item" style="display: flex; align-items: center; margin-bottom: 10px;">
                    <div class="guide-icon" style="margin-right: 10px;">
                        <img src="{{ icons['u28.png'] }}" alt="数图聚焦" style="width: 20px;height: 20px;">
                    </div>
                    <div class="guide-text">
                        <span class="guide-label" style="font-weight: bold;">数图聚焦：</span>
                        <span class="guide-value" style="margin-left: 5px;">{{ data.data_focus }}</span>
                    </div>
                </div>
                <div class="guide-item" style="display: flex; align-items: center; margin-bottom: 10px;">
                    <div class="guide-icon" style="margin-right: 10px;">
                        <img src="{{ icons['u29.png'] }}" alt="事件概览" style="width: 20px;height: 20px;">
                    </div>
                    <div class="guide-text">
                        <span class="guide-label" style="font-weight: bold;"> 事件概览：</span>
                        <span class="guide-value" style="margin-left: 5px;">{{ data.event_overview }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content" style="display: flex; gap: 20px;">
            <!-- 左侧内容 -->
            <div class="left-column" style="flex: 3;">
                <!-- 新闻内容 -->
                {% if data.news_list %}
                <div class="content-section" style="margin-bottom: 20px;">
                    <div class="section-header" style="margin-bottom: 10px;">
                        <h3 class="section-title" style="font-size: 18px; font-weight: bold;">新闻内容</h3>
                    </div>
                    <div class="section-content">
                        {% for news in data.news_list %}
                        <div class="news-item" style="margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                            <!-- 第一部分：标题 -->
                            <h4 class="news-title" style="font-size: 16px; font-weight: bold; margin-bottom: 10px;">{{ news.title }}</h4>
                            <!-- 第二部分：图片 -->
                            <div class="news-image-section" style="margin-bottom: 10px;">
                                {% if news.image %}
                                <div class="news-image">
                                    <img src="{{ news.image|to_base64 }}" alt="{{ news.title }}" class="news-img" style="max-width: 100%; height: auto;">
                                </div>
                                {% else %}
                                <div class="news-image-placeholder" style="width: 100%; height: 200px; background-color: #f0f0f0;">
                                    <!-- 图片占位区域 -->
                                </div>
                                {% endif %}
                            </div>
                            <!-- 第三部分：内容 -->
                            <div class="news-content-section">
                                <p class="news-text" style="font-size: 14px; line-height: 1.6; margin-bottom: 10px;">{{ news.content|nl2br }}</p>
                                <!-- 底部按钮和分享 -->
                                <div class="news-footer" style="display: flex; align-items: center; justify-content: space-between;">
                                    <button class="read-full-btn" style="background-color: #007BFF; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">阅读全文</button>
                                    <div class="share-wechat" style="display: flex; align-items: center;">
                                        <span class="share-label" style="font-size: 14px; margin-right: 5px;">分享：</span>
                                        <img src="{{ icons['u14.png'] }}" alt="分享到微信" class="wechat-icon" style="width: 20px; height: 20px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- 每日事件 -->
                {% if data.daily_events %}
                <div class="content-section daily-events-section" style="margin-bottom: 20px;">
                    <div class="section-header" style="margin-bottom: 10px;">
                        <h3 class="section-title" style="font-size: 18px; font-weight: bold;">新闻内容</h3>
                    </div>
                    <div class="section-content">
                        {% for event in data.daily_events %}
                        <div class="event-item" style="margin-bottom: 10px; padding: 10px; border: 1px solid #eee; border-radius: 4px;">
                            <div class="event-industry" style="font-size: 14px; font-weight: bold; margin-bottom: 5px;">{{ event.industry }}</div>
                            <div class="event-content" style="font-size: 14px; line-height: 1.6; margin-bottom: 5px;">{{ event.content|nl2br }}</div>
                            <div class="event-details-link" style="text-align: left;">
                                <a href="#" class="view-details" style="font-size: 14px; color: #007BFF; text-decoration: none;">查看交易详情&gt;</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- 数据图表 -->
                {% if data.chart_title or data.chart_image %}
                <div class="content-section" style="margin-bottom: 20px;">
                    <div class="section-header" style="margin-bottom: 10px;">
                        <h3 class="section-title" style="font-size: 18px; font-weight: bold;">数据图表</h3>
                    </div>
                    <div class="section-content">
                        <div class="chart-item">
                            <div class="chart-image-container" style="margin-bottom: 10px;">
                                {% if data.chart_image %}
                                <div class="chart-image">
                                    <img src="{{ data.chart_image|to_base64 }}" alt="{{ data.chart_title or '数据图表' }}" class="chart-img" style="max-width: 100%; height: auto;">
                                </div>
                                {% else %}
                                <div class="chart-image-placeholder" style="width: 100%; height: 300px; background-color: #f0f0f0;">
                                    <!-- 图表图片占位区域 -->
                                </div>
                                {% endif %}
                            </div>
                            {% if data.chart_title and data.chart_url %}
                            <div class="chart-source" style="font-size: 14px; color: #666;">
                               源自： <a href="{{ data.chart_url }}" target="_blank" class="chart-source-link" style="color: #007BFF; text-decoration: none;">{{ data.chart_title }}</a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- 右侧边栏 -->
            <div class="right-sidebar" style="flex: 1;">
                <!-- 主体索引 -->
                {% if data.main_index %}
                <div class="sidebar-section" style="margin-bottom: 20px;">
                    <h3 class="sidebar-title" style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">主体索引</h3>
                    <div class="index-list">
                        {% for index in data.main_index %}
                        <div class="index-item" style="margin-bottom: 5px;">
                            <div class="index-name">
                                <a href="{{ index|generate_entity_url }}" target="_blank" class="entity-link" style="font-size: 14px; color: #007BFF; text-decoration: none;">{{ index.name }}</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
