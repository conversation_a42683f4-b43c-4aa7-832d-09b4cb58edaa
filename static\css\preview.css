/* 预览页面样式 - 完全仿照图片中的日报格式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'SimSun', <PERSON><PERSON>, sans-serif;
    background: #1e3a5f;
    color: #333;
    line-height: 1.4;
}

.report-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
}

/* 头部样式 - 完全仿照图片中的样式 */
.header {
    color: #333;
    padding: 0;
    border-bottom: 1px solid #dee2e6;
}

.header-top {
    background: white;
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
}

.logo-section {
    display: flex;
    align-items: center;
}

.logo-container {
    width: 60px;
    height: 60px;
    background: #4a90e2;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.logo-image {
    width: 40px;
    height: 40px;
    object-fit: contain;
    filter: brightness(0) invert(1);
}

.company-info {
    display: flex;
    flex-direction: column;
}

.company-name-cn {
    font-size: 1.4em;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.company-name-en {
    font-size: 0.9em;
    color: #666;
    font-weight: 400;
    letter-spacing: 1px;
}

.header-bottom {
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-title {
    font-size: 1.8em;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 1px;
}

.report-date {
    font-size: 1.1em;
    color: #6c757d;
    font-weight: 500;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    gap: 20px;
    padding: 0;
    background: white;
}

.left-column {
    flex: 2;
    padding: 0;
}

.right-sidebar {
    flex: 1;
    background: #f0f4f8;
    padding: 20px;
    border-left: 1px solid #e0e6ed;
}

/* 今日导读样式 - 完全仿照图片 */
.today-guide {
    background: white;
    border-radius: 0;
    padding: 25px 30px;
    margin-bottom: 0;
}

.guide-title {
    color: #2c3e50;
    font-size: 1.6em;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 0;
    border-bottom: none;
}

.guide-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    font-size: 1em;
    line-height: 1.6;
}

.guide-item:last-child {
    margin-bottom: 0;
}

.guide-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
}

.guide-label {
    color: #2c3e50;
    font-weight: 600;
    margin-right: 8px;
    flex-shrink: 0;
    font-size: 1em;
}

.guide-text {
    color: #2c3e50;
    flex: 1;
    font-weight: 400;
}

/* 通用内容区块样式 */
.content-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    margin-bottom: 20px;
    overflow: hidden;
}

.section-header {
    background: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
}

.section-title {
    color: #333;
    font-size: 1.3em;
    font-weight: 600;
    margin: 0;
}

.section-content {
    padding: 15px;
}

/* 新闻项目样式 - 上中下三部分布局 */
.news-item {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.news-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* 第一部分：标题 */
.news-title-section {
    margin-bottom: 15px;
}

.news-title {
    color: #333;
    font-size: 1.1em;
    font-weight: 600;
    line-height: 1.4;
    margin: 0;
}

/* 第二部分：图片 */
.news-image-section {
    margin-bottom: 15px;
}

.news-image {
    width: 100%;
    margin-bottom: 15px;
}

.news-img {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.news-image-placeholder {
    width: 100%;
    height: 200px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 0.9em;
}

.news-image-placeholder::before {
    content: "图片占位区域";
}

/* 第三部分：内容 */
.news-content-section {
    margin-top: 0;
}

.news-text {
    color: #666;
    font-size: 0.9em;
    line-height: 1.6;
    margin-bottom: 20px;
    text-align: justify;
    white-space: normal; /* 允许文本换行 */
}

/* 底部按钮和分享区域 */
.news-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.read-full-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.read-full-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.share-wechat {
    display: flex;
    align-items: center;
    gap: 8px;
}

.share-label {
    color: #666;
    font-size: 0.85em;
}

.wechat-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wechat-icon:hover {
    transform: scale(1.1);
}

/* 每日事件样式 - 仿照图片 */
.daily-events-section {
    background: white;
    margin-bottom: 0;
}

.event-item {
    margin-bottom: 20px;
}

.event-item:last-child {
    margin-bottom: 20px; /* 保持底部间距，为链接留空间 */
}

.event-industry {
    font-weight: 600;
    font-size: 1.2em;
    margin-bottom: 12px;
    line-height: 1.4;
}

.event-content {
    color: #666;
    font-size: 0.9em;
    line-height: 1.6;
    text-align: justify;
    margin-bottom: 0;
}

/* 查看交易详情链接 */
.event-details-link {
    padding-top: 5px;
    padding-bottom: 5px;
}

.view-details {
    color: #4a90e2;
    font-size: 0.9em;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.view-details:hover {
    color: #357abd;
    text-decoration: underline;
}

/* 图表样式 - 新版本 */
.chart-item {
    margin-bottom: 25px;
    text-align: left;
}

.chart-item:last-child {
    margin-bottom: 0;
}

.chart-image-container {
    margin-bottom: 15px;
}

.chart-image {
    width: 100%;
    margin-bottom: 15px;
}

.chart-img {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.chart-image-placeholder {
    width: 100%;
    height: 300px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 1.1em;
}

.chart-image-placeholder::before {
    content: "图表图片占位区域";
}

.chart-source {
    align-items: center;
    font-size: 0.9em;
    margin-top: 10px;
}

.source-label {
    color: #666;
    margin-right: 5px;
}

.chart-source-link {
    color: #4a90e2;
    text-decoration: none;
    transition: all 0.3s ease;
}

.chart-source-link:hover {
    color: #357abd;
    text-decoration: underline;
}

/* 右侧边栏样式 */
.sidebar-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    margin-bottom: 15px;
    overflow: hidden;
}

.sidebar-title {
    background: #f8f9fa;
    color: #333;
    font-size: 0.9em;
    font-weight: 600;
    padding: 10px 15px;
    margin: 0;
}

/* 主体索引样式 */
.index-list {
    padding: 15px;
}



.index-item:last-child {
    margin-bottom: 0;
}

.index-name {
    color: #333;
    font-weight: 600;
    font-size: 0.85em;
    margin-bottom: 3px;
}

.entity-link {
    color: #4a90e2;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.entity-link:hover {
    color: #357abd;
    text-decoration: underline;
}

.index-identity {
    color: #666;
    font-size: 0.8em;
    margin-bottom: 3px;
}

.index-id {
    color: #999;
    font-size: 0.75em;
}

/* 快速导航样式 */
.quick-nav {
    padding: 15px;
}

.nav-link {
    display: block;
    padding: 8px 12px;
    margin-bottom: 5px;
    background: #f8f9fa;
    color: #555;
    text-decoration: none;
    border-radius: 3px;
    font-size: 0.85em;
    transition: all 0.3s;
}

.nav-link:last-child {
    margin-bottom: 0;
}

.nav-link:hover {
    background: #e9ecef;
    color: #333;
}

/* 底部样式 */
.footer {
    background: #2c3e50;
    color: white;
    padding: 15px 20px;
    margin-top: 30px;
    font-size: 0.85em;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 操作按钮 */
.action-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    font-size: 0.85em;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.btn-primary {
    background: #4a90e2;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .report-container {
        margin: 0;
        border-radius: 0;
    }

    .header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .main-content {
        flex-direction: column;
        gap: 0;
    }

    .right-sidebar {
        border-left: none;
        border-top: 1px solid #e0e6ed;
    }

    .action-buttons {
        position: static;
        justify-content: center;
        margin: 20px;
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
    }

    .action-buttons {
        display: none;
    }

    .report-container {
        box-shadow: none;
        max-width: none;
    }

    .content-section,
    .sidebar-section {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* 数图聚焦样式 */
.data-focus-item {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.focus-image {
    flex-shrink: 0;
}

.placeholder-image {
    width: 120px;
    height: 80px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2em;
    color: white;
}

.focus-content {
    flex: 1;
}

.focus-title {
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 10px;
    font-weight: 600;
}

.focus-text {
    color: #555;
    margin-bottom: 10px;
}

/* 新闻列表样式 */
.news-item {
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ecf0f1;
}

.news-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.news-image .placeholder-image {
    width: 100px;
    height: 70px;
    font-size: 1.5em;
}

.news-content {
    flex: 1;
}

.news-title {
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 8px;
    font-weight: 600;
}

.news-text {
    color: #555;
    margin-bottom: 10px;
    font-size: 0.95em;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85em;
}

.news-time {
    color: #7f8c8d;
}

/* 每日事件样式 */
.event-item {
    margin-bottom: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.event-industry {
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.event-content {
    color: #555;
    font-size: 0.95em;
}

/* 报告图表样式 */
.chart-item {
    margin-bottom: 25px;
    text-align: center;
}

.chart-title {
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 15px;
    font-weight: 600;
}

.chart-placeholder {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-radius: 10px;
    padding: 40px;
    margin-bottom: 15px;
    color: white;
}

.chart-icon {
    font-size: 3em;
    margin-bottom: 10px;
}

.chart-text {
    font-size: 1.1em;
    font-weight: 500;
}

.chart-link a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.chart-link a:hover {
    text-decoration: underline;
}

/* 右侧边栏样式 */
.sidebar-section {
    margin-bottom: 25px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.sidebar-title {
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 15px;
    padding-bottom: 8px;
}

.overview-content {
    color: #555;
    font-size: 0.95em;
    line-height: 1.6;
}

/* 主体索引样式 */
.index-item {
    margin-bottom: 15px;
    border-radius: 6px;
}

.index-name {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 3px;
}

.index-identity {
    color: #7f8c8d;
    font-size: 0.9em;
    margin-bottom: 3px;
}

.index-id {
    color: #95a5a6;
    font-size: 0.85em;
}

/* 快速导航样式 */
.nav-link {
    padding: 10px 15px;
    margin-bottom: 8px;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    border-left-color: #3498db;
    background: #ecf0f1;
}

/* 底部样式 */
.footer {
    background: #2c3e50;
    color: white;
    padding: 20px 30px;
    margin-top: 40px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9em;
}

/* 操作按钮 */
.action-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    gap: 15px;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .main-content {
        flex-direction: column;
        padding: 20px;
    }
    
    .data-focus-item,
    .news-item {
        flex-direction: column;
    }
    
    .action-buttons {
        position: static;
        justify-content: center;
        margin: 20px;
    }
}

/* 打印样式 */
@media print {
    .action-buttons {
        display: none;
    }
    
    .report-container {
        box-shadow: none;
    }
    
    .section {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
