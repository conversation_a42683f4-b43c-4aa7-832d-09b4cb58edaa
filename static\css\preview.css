/* 预览页面样式 - 仿照图片中的日报格式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f7fa;
    line-height: 1.6;
}

.report-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-icon {
    font-size: 2.5em;
    margin-right: 15px;
}

.main-title {
    font-size: 1.8em;
    font-weight: 600;
    margin-bottom: 5px;
}

.sub-title {
    font-size: 1em;
    opacity: 0.8;
}

.nav-menu {
    display: flex;
    gap: 20px;
}

.nav-item {
    padding: 8px 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-item:hover {
    background: rgba(255,255,255,0.2);
}

/* 主要内容区域 */
.main-content {
    display: flex;
    gap: 30px;
    padding: 30px;
}

.left-column {
    flex: 2;
}

.right-sidebar {
    flex: 1;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    height: fit-content;
}

/* 通用section样式 */
.section {
    margin-bottom: 35px;
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border-left: 4px solid #3498db;
}

.section-title {
    color: #2c3e50;
    font-size: 1.4em;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
}

/* 今日导读样式 */
.today-guide-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #007bff;
}

.guide-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 12px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.guide-item:last-child {
    margin-bottom: 0;
}

.guide-icon {
    font-size: 1.2em;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
}

.guide-content {
    flex: 1;
    line-height: 1.5;
}

.guide-label {
    color: #2c3e50;
    font-weight: 600;
    margin-right: 5px;
}

.guide-text {
    color: #555;
}

/* 数图聚焦样式 */
.data-focus-item {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.focus-image {
    flex-shrink: 0;
}

.placeholder-image {
    width: 120px;
    height: 80px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2em;
    color: white;
}

.focus-content {
    flex: 1;
}

.focus-title {
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 10px;
    font-weight: 600;
}

.focus-text {
    color: #555;
    margin-bottom: 10px;
}

/* 新闻列表样式 */
.news-item {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ecf0f1;
}

.news-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.news-image .placeholder-image {
    width: 100px;
    height: 70px;
    font-size: 1.5em;
}

.news-content {
    flex: 1;
}

.news-title {
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 8px;
    font-weight: 600;
}

.news-text {
    color: #555;
    margin-bottom: 10px;
    font-size: 0.95em;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85em;
}

.news-time {
    color: #7f8c8d;
}

/* 每日事件样式 */
.event-item {
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #e74c3c;
}

.event-industry {
    color: #e74c3c;
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.event-content {
    color: #555;
    font-size: 0.95em;
}

/* 报告图表样式 */
.chart-item {
    margin-bottom: 25px;
    text-align: center;
}

.chart-title {
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 15px;
    font-weight: 600;
}

.chart-placeholder {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-radius: 10px;
    padding: 40px;
    margin-bottom: 15px;
    color: white;
}

.chart-icon {
    font-size: 3em;
    margin-bottom: 10px;
}

.chart-text {
    font-size: 1.1em;
    font-weight: 500;
}

.chart-link a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.chart-link a:hover {
    text-decoration: underline;
}

/* 右侧边栏样式 */
.sidebar-section {
    margin-bottom: 25px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.sidebar-title {
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #3498db;
}

.overview-content {
    color: #555;
    font-size: 0.95em;
    line-height: 1.6;
}

/* 主体索引样式 */
.index-item {
    margin-bottom: 15px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #9b59b6;
}

.index-name {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 3px;
}

.index-identity {
    color: #7f8c8d;
    font-size: 0.9em;
    margin-bottom: 3px;
}

.index-id {
    color: #95a5a6;
    font-size: 0.85em;
}

/* 快速导航样式 */
.nav-link {
    padding: 10px 15px;
    margin-bottom: 8px;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    border-left-color: #3498db;
    background: #ecf0f1;
}

/* 底部样式 */
.footer {
    background: #2c3e50;
    color: white;
    padding: 20px 30px;
    margin-top: 40px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9em;
}

/* 操作按钮 */
.action-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    gap: 15px;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .main-content {
        flex-direction: column;
        padding: 20px;
    }
    
    .data-focus-item,
    .news-item {
        flex-direction: column;
    }
    
    .action-buttons {
        position: static;
        justify-content: center;
        margin: 20px;
    }
}

/* 打印样式 */
@media print {
    .action-buttons {
        display: none;
    }
    
    .report-container {
        box-shadow: none;
    }
    
    .section {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
